
import React from 'react';
import Navbar from "@/components/layout/Navbar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Calendar, Video, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';

const Index = () => {

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50">
      <Navbar />

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4">
            <div className="max-w-7xl mx-auto">
              {/* Main Profile Card */}
              <Card className="mb-8 overflow-hidden shadow-2xl border-0 bg-white/90 backdrop-blur-sm">
                <CardContent className="p-0">
                  <div className="grid lg:grid-cols-5 gap-0">
                    {/* Profile Image */}
                    <div className="lg:col-span-2 relative bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center overflow-hidden">
                      <div className="w-full h-96 lg:h-[500px] relative">
                        <img
                          src="/Drekochin portrait.png"
                          alt="Dr. Fintan Ekochin"
                          className="w-full h-full object-cover object-center"
                          style={{ display: 'block' }}
                          onError={(e) => {
                            console.log('Image failed to load:', e);
                            const target = e.currentTarget;
                            target.style.display = 'none';
                            const fallback = target.parentElement?.querySelector('.fallback-avatar');
                            if (fallback) {
                              (fallback as HTMLElement).style.display = 'flex';
                            }
                          }}
                        />
                        <div
                          className="fallback-avatar absolute inset-0 bg-gradient-to-br from-blue-100 to-indigo-200 flex items-center justify-center text-6xl font-bold text-blue-600"
                          style={{ display: 'none' }}
                        >
                          FE
                        </div>
                      </div>
                    </div>

                    {/* Profile Info */}
                    <div className="lg:col-span-3 p-8 lg:p-12 flex flex-col justify-center bg-gradient-to-r from-white to-blue-50">
                      <div className="mb-4">
                        <h1 className="text-4xl md:text-5xl font-bold mb-3 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                          Dr. Fintan Ekochin, MD
                        </h1>
                        <p className="text-xl md:text-2xl text-blue-600 font-medium mb-6">
                          Fellow WACP • Neurologist • Integrative Medicine Specialist
                        </p>
                      </div>

                      <div className="space-y-4 mb-8">
                        <p className="text-gray-600 text-base leading-relaxed">
                          Dr. Ekochin Fintan is one of two generations of the EKOCHIN Family of Doctors. He largely grew up in
                          Nigeria with some years of childhood spent in Austria, where he added German to his Igbo and English
                          language proficiency.
                        </p>

                        <div className="flex flex-wrap gap-2 mb-6">
                          <span className="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full font-medium">Fellow WACP</span>
                          <span className="px-3 py-1 bg-indigo-100 text-indigo-700 text-sm rounded-full font-medium">Integrative Medicine</span>
                          <span className="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full font-medium">Lifestyle Medicine</span>
                          <span className="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full font-medium">Former Health Commissioner</span>
                        </div>
                      </div>

                      <div className="flex flex-col sm:flex-row gap-4">
                        <Link to="/booking" className="flex-1">
                          <Button className="w-full py-4 px-8 text-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                            Book Consultation
                            <ArrowRight className="ml-2 h-5 w-5" />
                          </Button>
                        </Link>
                        <Link to="/about" className="flex-1">
                          <Button variant="outline" className="w-full py-4 px-8 text-lg border-2 border-blue-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-300">
                            Learn More
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Features Grid */}
              <div className="grid md:grid-cols-3 gap-6 mb-12">
                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <CardContent className="p-8 text-center">
                    <Video className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                    <h3 className="font-bold text-xl mb-3 text-gray-900">HD Video Consultations</h3>
                    <p className="text-base text-gray-600">Crystal clear video calls with screen sharing</p>
                  </CardContent>
                </Card>

                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <CardContent className="p-8 text-center">
                    <Calendar className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                    <h3 className="font-bold text-xl mb-3 text-gray-900">Smart Scheduling</h3>
                    <p className="text-base text-gray-600">AI-powered appointment booking system</p>
                  </CardContent>
                </Card>

                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  <CardContent className="p-8 text-center">
                    <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                    <h3 className="font-bold text-xl mb-3 text-gray-900">Secure & Private</h3>
                    <p className="text-base text-gray-600">HIPAA compliant with end-to-end encryption</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default Index;
