
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuthStatus } from '@/hooks/use-auth-status';
import { Navigate, Link } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import {
  Calendar,
  Clock,
  FileText,
  Pill,
  User,
  Video,
  Phone,
  Download,
  Settings,
  Bell,
  MessageSquare,
  Activity,
  Heart,
  TrendingUp,
  ChevronRight,
  Loader2
} from 'lucide-react';
import { format } from 'date-fns';

// Import our new utilities
import { useMultiApiState } from '@/hooks/use-api-state';
import { CardSkeleton, ErrorState, EmptyState, RefreshIndicator } from '@/components/ui/loading-states';
import { formatters, statusTransforms } from '@/lib/utils/dataTransforms';

// Import our new API services
import {
  appointmentApiService,
  medicalService,
  type Appointment as ApiAppointment,
  type Prescription as ApiPrescription,
  type MedicalRecord as ApiMedicalRecord
} from '@/services';

// Local interfaces for component state (mapped from API types)
interface Appointment {
  id: string;
  date: Date;
  time: string;
  type: 'video' | 'audio';
  status: 'scheduled' | 'completed' | 'cancelled';
  doctor: string;
  notes?: string;
}

interface Prescription {
  id: string;
  medication: string;
  dosage: string;
  frequency: string;
  startDate: Date;
  endDate: Date;
  status: 'active' | 'completed' | 'discontinued';
  instructions: string;
}

interface HealthRecord {
  id: string;
  date: Date;
  type: 'consultation' | 'lab_result' | 'prescription' | 'note';
  title: string;
  content: string;
  attachments?: string[];
}

// Helper functions to transform API data to component format
const transformAppointment = (apiAppointment: ApiAppointment): Appointment => ({
  id: apiAppointment.id,
  date: new Date(apiAppointment.appointmentDate),
  time: new Date(apiAppointment.appointmentDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
  type: apiAppointment.consultationType.toLowerCase() as 'video' | 'audio',
  status: apiAppointment.status.toLowerCase() as 'scheduled' | 'completed' | 'cancelled',
  doctor: apiAppointment.provider?.user?.name || 'Dr. Fintan Ekochin',
  notes: apiAppointment.reason || apiAppointment.notes
});

const transformPrescription = (apiPrescription: ApiPrescription): Prescription => ({
  id: apiPrescription.id,
  medication: apiPrescription.medication,
  dosage: apiPrescription.dosage,
  frequency: apiPrescription.frequency,
  startDate: new Date(apiPrescription.startDate),
  endDate: apiPrescription.endDate ? new Date(apiPrescription.endDate) : new Date(),
  status: apiPrescription.status as 'active' | 'completed' | 'discontinued',
  instructions: apiPrescription.instructions
});

const transformHealthRecord = (apiRecord: ApiMedicalRecord): HealthRecord => ({
  id: apiRecord.id,
  date: new Date(apiRecord.createdAt),
  type: apiRecord.type as 'consultation' | 'lab_result' | 'prescription' | 'note',
  title: apiRecord.title,
  content: apiRecord.content,
  attachments: apiRecord.attachments
});

const PatientDashboard: React.FC = () => {
  const isMobile = useIsMobile();
  const { user, isAuthenticated, isLoading: authLoading } = useAuthStatus();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');

  // Use multi-API state hook for managing all data
  const {
    states,
    execute,
    resetAll,
    isLoading,
    hasError
  } = useMultiApiState({
    appointments: [] as Appointment[],
    prescriptions: [] as Prescription[],
    healthRecords: [] as HealthRecord[]
  });

  // Load all data
  const loadAllData = async () => {
    if (!isAuthenticated || !user) return;

    // Load appointments
    await execute('appointments', async () => {
      const response = await appointmentApiService.getMyAppointments({ limit: 10 });
      if (response.success && response.data?.appointments) {
        return response.data.appointments.map(transformAppointment);
      }
      return [];
    });

    // Load prescriptions
    await execute('prescriptions', async () => {
      const response = await medicalService.getMyPrescriptions({ limit: 10 });
      if (response.success && response.data?.prescriptions) {
        return response.data.prescriptions.map(transformPrescription);
      }
      return [];
    });

    // Load health records
    await execute('healthRecords', async () => {
      const response = await medicalService.getMyMedicalRecords({ limit: 10 });
      if (response.success && response.data?.records) {
        return response.data.records.map(transformHealthRecord);
      }
      return [];
    });
  };

  // Load data when component mounts and user is authenticated
  useEffect(() => {
    loadAllData();
  }, [isAuthenticated, user]);

  // Show loading spinner while authenticating
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading dashboard...</span>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/auth/login" replace />;
  }

  // Extract data from states
  const appointments = states.appointments.data || [];
  const prescriptions = states.prescriptions.data || [];
  const healthRecords = states.healthRecords.data || [];

  // Filter data for quick stats
  const upcomingAppointments = appointments.filter(apt => apt.status === 'scheduled');
  const activePrescriptions = prescriptions.filter(rx => rx.status === 'active');

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-blue-900 ${isMobile ? 'mobile-app-container' : ''}`}>
      <div className={`${isMobile ? 'mobile-content p-4' : 'container mx-auto px-4 py-8'}`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-gray-900 dark:text-gray-100`}>
              Welcome back, {user?.name || 'Patient'}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Your health journey with Dr. Fintan Ekochin
            </p>
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            {!isMobile && 'Settings'}
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className={`grid w-full ${isMobile ? 'grid-cols-2' : 'grid-cols-4'}`}>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="appointments">Appointments</TabsTrigger>
            {!isMobile && <TabsTrigger value="prescriptions">Prescriptions</TabsTrigger>}
            {!isMobile && <TabsTrigger value="records">Health Records</TabsTrigger>}
            {isMobile && <TabsTrigger value="more">More</TabsTrigger>}
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Quick Stats */}
            <div className={`grid ${isMobile ? 'grid-cols-2' : 'grid-cols-4'} gap-4`}>
              <Card>
                <CardContent className="p-4 text-center">
                  <Calendar className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {states.appointments.loading ? (
                      <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                    ) : (
                      upcomingAppointments.length
                    )}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Upcoming
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 text-center">
                  <Pill className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {states.prescriptions.loading ? (
                      <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                    ) : (
                      activePrescriptions.length
                    )}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Active Rx
                  </div>
                </CardContent>
              </Card>

              {!isMobile && (
                <>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <FileText className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {states.healthRecords.loading ? (
                          <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                        ) : (
                          healthRecords.length
                        )}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        Records
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4 text-center">
                      <Heart className="h-8 w-8 text-red-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        95%
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        Health Score
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>

            {/* Refresh indicator */}
            <div className="flex justify-end">
              <RefreshIndicator
                isRefreshing={isLoading()}
                onRefresh={loadAllData}
                lastUpdated={states.appointments.lastUpdated || undefined}
              />
            </div>

            {/* Next Appointment */}
            {upcomingAppointments.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Next Appointment
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold text-lg">
                        {format(upcomingAppointments[0].date, 'EEEE, MMMM d')}
                      </div>
                      <div className="text-gray-600 dark:text-gray-400">
                        {upcomingAppointments[0].time} • {upcomingAppointments[0].doctor}
                      </div>
                      <Badge variant="secondary" className="mt-2">
                        {upcomingAppointments[0].type === 'video' ? (
                          <><Video className="h-3 w-3 mr-1" /> Video Call</>
                        ) : (
                          <><Phone className="h-3 w-3 mr-1" /> Audio Call</>
                        )}
                      </Badge>
                    </div>
                    <Button>
                      Join Call
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`grid ${isMobile ? 'grid-cols-2' : 'grid-cols-4'} gap-4`}>
                  <Link to="/booking">
                    <Button variant="outline" className="w-full h-20 flex-col gap-2">
                      <Calendar className="h-6 w-6" />
                      Book Appointment
                    </Button>
                  </Link>
                  
                  <Button variant="outline" className="w-full h-20 flex-col gap-2">
                    <MessageSquare className="h-6 w-6" />
                    Send Message
                  </Button>
                  
                  <Button variant="outline" className="w-full h-20 flex-col gap-2">
                    <Download className="h-6 w-6" />
                    Download Records
                  </Button>
                  
                  <Button variant="outline" className="w-full h-20 flex-col gap-2">
                    <Bell className="h-6 w-6" />
                    Notifications
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Appointments Tab */}
          <TabsContent value="appointments" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Your Appointments</h2>
              <Link to="/booking">
                <Button>
                  <Calendar className="h-4 w-4 mr-2" />
                  Book New
                </Button>
              </Link>
            </div>

            {appointmentsLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span>Loading appointments...</span>
                </div>
              </div>
            ) : appointments.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No appointments yet</h3>
                  <p className="text-gray-600 mb-4">Book your first appointment to get started</p>
                  <Link to="/booking">
                    <Button>
                      <Calendar className="h-4 w-4 mr-2" />
                      Book Appointment
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {appointments.map((appointment) => (
                  <Card key={appointment.id}>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className={`p-3 rounded-full ${
                            appointment.status === 'scheduled' ? 'bg-blue-100 text-blue-600' :
                            appointment.status === 'completed' ? 'bg-green-100 text-green-600' :
                            'bg-red-100 text-red-600'
                          }`}>
                            {appointment.type === 'video' ? <Video className="h-5 w-5" /> : <Phone className="h-5 w-5" />}
                          </div>

                          <div>
                            <div className="font-semibold">
                              {format(appointment.date, 'MMMM d, yyyy')} at {appointment.time}
                            </div>
                            <div className="text-gray-600 dark:text-gray-400">
                              {appointment.doctor}
                            </div>
                            {appointment.notes && (
                              <div className="text-sm text-gray-500 dark:text-gray-500 mt-1">
                                {appointment.notes}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge variant={
                            appointment.status === 'scheduled' ? 'default' :
                            appointment.status === 'completed' ? 'secondary' :
                            'destructive'
                          }>
                            {appointment.status}
                          </Badge>

                          {appointment.status === 'scheduled' && (
                            <Button size="sm">
                              Join
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Mobile More Tab */}
          {isMobile && (
            <TabsContent value="more" className="space-y-6">
              <div className="grid grid-cols-1 gap-4">
                <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('prescriptions')}>
                  <CardContent className="p-4 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Pill className="h-6 w-6 text-green-600" />
                      <div>
                        <div className="font-semibold">Prescriptions</div>
                        <div className="text-sm text-gray-600">{activePrescriptions.length} active medications</div>
                      </div>
                    </div>
                    <ChevronRight className="h-5 w-5 text-gray-400" />
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('records')}>
                  <CardContent className="p-4 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <FileText className="h-6 w-6 text-purple-600" />
                      <div>
                        <div className="font-semibold">Health Records</div>
                        <div className="text-sm text-gray-600">{healthRecords.length} documents</div>
                      </div>
                    </div>
                    <ChevronRight className="h-5 w-5 text-gray-400" />
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          )}

          {/* Prescriptions Tab */}
          <TabsContent value="prescriptions" className="space-y-6">
            <h2 className="text-2xl font-bold">Your Prescriptions</h2>

            {prescriptionsLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span>Loading prescriptions...</span>
                </div>
              </div>
            ) : prescriptions.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Pill className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No prescriptions yet</h3>
                  <p className="text-gray-600">Your prescriptions will appear here after consultations</p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {prescriptions.map((prescription) => (
                  <Card key={prescription.id}>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4">
                          <div className="p-3 bg-green-100 text-green-600 rounded-full">
                            <Pill className="h-5 w-5" />
                          </div>

                          <div className="flex-1">
                            <div className="font-semibold text-lg">{prescription.medication}</div>
                            <div className="text-gray-600 dark:text-gray-400 mb-2">
                              {prescription.dosage} • {prescription.frequency}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-500">
                              {prescription.instructions}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                              {format(prescription.startDate, 'MMM d')} - {format(prescription.endDate, 'MMM d, yyyy')}
                            </div>
                          </div>
                        </div>

                        <Badge variant={prescription.status === 'active' ? 'default' : 'secondary'}>
                          {prescription.status}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Health Records Tab */}
          <TabsContent value="records" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Health Records</h2>
              <Button variant="outline" disabled={recordsLoading || healthRecords.length === 0}>
                <Download className="h-4 w-4 mr-2" />
                Export All
              </Button>
            </div>

            {recordsLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span>Loading health records...</span>
                </div>
              </div>
            ) : healthRecords.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No health records yet</h3>
                  <p className="text-gray-600">Your medical records and consultation notes will appear here</p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {healthRecords.map((record) => (
                  <Card key={record.id}>
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className={`p-3 rounded-full ${
                          record.type === 'consultation' ? 'bg-blue-100 text-blue-600' :
                          record.type === 'lab_result' ? 'bg-purple-100 text-purple-600' :
                          record.type === 'prescription' ? 'bg-green-100 text-green-600' :
                          'bg-gray-100 text-gray-600'
                        }`}>
                          {record.type === 'consultation' ? <User className="h-5 w-5" /> :
                           record.type === 'lab_result' ? <Activity className="h-5 w-5" /> :
                           record.type === 'prescription' ? <Pill className="h-5 w-5" /> :
                           <FileText className="h-5 w-5" />}
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-semibold">{record.title}</div>
                            <div className="text-sm text-gray-500">
                              {format(record.date, 'MMM d, yyyy')}
                            </div>
                          </div>
                          <div className="text-gray-600 dark:text-gray-400">
                            {record.content}
                          </div>
                          {record.attachments && record.attachments.length > 0 && (
                            <div className="mt-3 flex gap-2">
                              {record.attachments.map((attachment, index) => (
                                <Button key={index} variant="outline" size="sm">
                                  <Download className="h-3 w-3 mr-1" />
                                  {attachment}
                                </Button>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default PatientDashboard;
