import { BrowserRouter, Routes, Route } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "./components/theme/ThemeProvider";
import Index from "./pages/Index";
import LoginPage from "./pages/auth/login";
import RegisterPage from "./pages/auth/register";
import AboutPage from "./pages/AboutPage";
import ServicesPage from "./pages/ServicesPage";
import BookingPage from "./pages/BookingPage";
import ContactPage from "./pages/ContactPage";
import FaqPage from "./pages/FaqPage";

const queryClient = new QueryClient();

const TestPage = () => (
  <div style={{
    padding: '20px',
    fontFamily: 'Arial, sans-serif',
    backgroundColor: '#f0f8ff',
    minHeight: '100vh'
  }}>
    <h1 style={{ color: '#2563eb', fontSize: '2rem', marginBottom: '1rem' }}>
      Dr. <PERSON><PERSON> Virtual Care Hub - PROVIDERS TEST
    </h1>
    <p style={{ fontSize: '1.2rem', color: '#374151', marginBottom: '2rem' }}>
      Testing if providers are working...
    </p>
    <div style={{
      backgroundColor: 'white',
      padding: '2rem',
      borderRadius: '8px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
    }}>
      <h2 style={{ color: '#1f2937', marginBottom: '1rem' }}>System Status</h2>
      <p style={{ color: '#059669' }}>✅ React App is working correctly!</p>
      <p style={{ color: '#059669' }}>✅ React Router is working!</p>
      <p style={{ color: '#059669' }}>✅ QueryClient provider loaded!</p>
      <p style={{ color: '#059669' }}>✅ ThemeProvider loaded!</p>
    </div>
  </div>
);

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/services" element={<ServicesPage />} />
            <Route path="/booking" element={<BookingPage />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/faq" element={<FaqPage />} />
            <Route path="/auth/login" element={<LoginPage />} />
            <Route path="/auth/register" element={<RegisterPage />} />
            <Route path="*" element={<TestPage />} />
          </Routes>
        </BrowserRouter>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
