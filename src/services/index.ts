// Export API client
export { apiClient, ApiClient } from './api';
export type { ApiResponse, ApiError } from './api';

// Export auth service (existing)
export { default as authService } from './authService';

// Export user service
export { userService } from './userService';
export type {
  User,
  Patient,
  Provider,
  CreateUserRequest,
  UpdateUserRequest,
  UserListResponse,
  UserListParams,
} from './userService';

// Export appointment service
export { appointmentApiService } from './appointmentApiService';
export type {
  Appointment,
  Consultation,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
  AppointmentListParams,
  AppointmentListResponse,
  TimeSlot,
  AvailabilityResponse,
} from './appointmentApiService';

// Export admin service
export { adminService } from './adminService';
export type {
  SystemStats,
  DashboardData,
  AdminUserListParams,
  AdminUserListResponse,
  AdminAppointmentListParams,
  AdminAppointmentListResponse,
  SystemSettings,
} from './adminService';

// Export medical service
export { medicalService } from './medicalService';
export type {
  MedicalRecord,
  Prescription,
  CreateMedicalRecordRequest,
  UpdateMedicalRecordRequest,
  CreatePrescriptionRequest,
  UpdatePrescriptionRequest,
  MedicalRecordListParams,
  PrescriptionListParams,
  MedicalRecordListResponse,
  PrescriptionListResponse,
} from './medicalService';

// Re-export existing services for compatibility
export { default as audioCallService } from './audioCallService';
export { default as calendarIntegrationService } from './calendarIntegrationService';
export { default as calendarService } from './calendarService';
export { default as notificationService } from './notificationService';
export { default as videoCallService } from './videoCallService';
export { default as webrtcService } from './webrtcService';

// Service instances for easy access
export const services = {
  auth: authService,
  user: userService,
  appointment: appointmentApiService,
  admin: adminService,
  medical: medicalService,
  audioCall: audioCallService,
  calendarIntegration: calendarIntegrationService,
  calendar: calendarService,
  notification: notificationService,
  videoCall: videoCallService,
  webrtc: webrtcService,
};

export default services;
